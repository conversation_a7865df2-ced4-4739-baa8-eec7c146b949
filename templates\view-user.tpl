<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
<meta charset="UTF-8">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no" name="viewport">
<title>{$site_name} — {$site_description}</title>
<link rel="shortcut icon" href="{$site_logo}" type="image/x-icon">
<link rel="icon" href="{$site_logo}" type="image/x-icon">

<link rel="stylesheet" href="{$base_url}dist/modules/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" integrity="sha512-1ycn6IcaQQ40/MKBW2W4Rhis/DbILU74C1vSrLJxCq57o941Ym01SwNsOMqvEBFlcgUa6xLiPY/NS5R+E6ztJQ==" crossorigin="anonymous" referrerpolicy="no-referrer" />

<link rel="stylesheet" href="{$base_url}dist/modules/bootstrap-social/bootstrap-social.css">
<link rel="stylesheet" href="{$base_url}dist/modules/datatables/datatables.min.css">
<link rel="stylesheet" href="{$base_url}dist/modules/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="{$base_url}dist/modules/datatables/Select-1.2.4/css/select.bootstrap4.min.css">
<link rel="stylesheet" href="{$base_url}dist/modules/select2.min.css">
<link rel="stylesheet" href="{$base_url}dist/modules/summernote/summernote-bs4.css">
<link rel="stylesheet" href="{$base_url}dist/sweetalert2/sweetalert2.min.css">
<link rel="stylesheet" href="{$base_url}dist/css-{$site_theme}/style.css">
<link rel="stylesheet" href="{$base_url}dist/css-{$site_theme}/components.css">
{include file='css/custom_css.tpl'}
</head>
<body>
    
<div class="center" id="loading">
    <div class='building-blocks'>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
    </div>
</div>

<div class="main-wrapper">
{include file='apps/topnav.tpl'}
{include file='apps/sidenav.tpl'}

<div class="main-content">
<section class="section">
<div class="section-header">
<h1>User List</h1>
<div class="section-header-breadcrumb">
<div class="breadcrumb-item">Main</div>
<div class="breadcrumb-item active">Manage</div>
<div class="breadcrumb-item active">User List</div>
</div>
</div>
<div class="section-body">
<div class="row">
<div class="col-md-3">
<div class="card card-primary">
<div class="card-header">
<h2 class="section-title">User Data</h2>
</div>
<div class="card-body">

<div class="form-group">
<label>Select Data</label>
<select class="form-control select2 selecttable" id="tabletype" data-minimum-results-for-search="-1">
<option value="normal">Normal</option>
<option value="bulk">Bulk</option>
<option value="inactive">Inactive</option>
<option value="trial">Trial</option>
</select>
</div>
<div class="form-group">
<label for="password">Current Data</label>
<input class="form-control currentloaded" type="text" value="Normal Users" readonly>
</div>
<div id="ActiveCounter">
<div class="form-group">
<label for="Active">Active Pin</label>
<input class="form-control Active" type="text" readonly>
</div>
<div class="form-group">
<label for="Inactive">Inactive Pin</label>
<input class="form-control Inactive" type="text" readonly>
</div>
<div class="form-group">
<label for="Total">Total Pin</label>
<input class="form-control Total" type="text" readonly>
</div>
</div>

<div class="pdata">
<hr>
<div class="btn-group btn-group-md" role="group">
<button class="btn btn-icon btn-primary mr-1 btn-print"><i class="fas fa-print"></i> PRINT</button>
</div>
</div>

</div>
</div>
</div>
<div class="col-md-9">
<div class="card card-primary">
<div class="card-header">
<h2 class="section-title currentuser">Normal User List</h2>
</div>
<div class="card-body">
<div class="alert alert-primary" role="alert">
    <strong>BANDWIDTH MONITORING : </strong> is available only on openvpn servers.<br>
    <strong>ONLINE MONITORING : </strong> is available only on openvpn, ssh, openconnect servers.
</div>
<table class="table table-striped table-listuser" style="width: 100%">
<thead>
<tr>
<th>Username</th>
<th>Online</th>
<th>Session</th>
<th>Usage</th>
<th>Expiration</th>
<th>Action</th>
</tr>
</thead>
</table>
</div>
</div>
</div>
</div>
</div>
</section>
</div>

<div class="modal fade normal-modalize" role="dialog" aria-labelledby="smallmodal">
<div class="modal-dialog modal-md normal-modal-dialog" role="document">
<div class="modal-content normal-modal-content">
<div class="modal-header normal-modal-header">
<h5 class="modal-title normal-modal-title"></h5>
<button type="button" class="close" data-dismiss="modal">&times;</button>
</div>
<div class="modal-body normal-modal-body">
<div class="modal-error normal-modal-error"></div>
<div class="modal-html normal-modal-html"></div>
</div>
</div>
</div>
</div>

<div class="modal fade search-modalize" role="dialog" aria-labelledby="smallmodal">
<div class="modal-dialog modal-md search-modal-dialog" role="document">
<div class="modal-content search-modal-content">
<div class="modal-header search-modal-header">
<h5 class="modal-title search-modal-title"></h5>
<button type="button" class="close" data-dismiss="modal">&times;</button>
</div>
<div class="modal-body search-modal-body">
<div class="modal-error search-modal-error"></div>
<div class="modal-html search-modal-html"></div>
</div>
</div>
</div>
</div>

<script src="{$base_url}dist/modules/jquery.min.js"></script>
<script src="{$base_url}dist/modules/popper.js"></script>
<script src="{$base_url}dist/modules/tooltip.js"></script>
<script src="{$base_url}dist/modules/bootstrap/js/bootstrap.min.js"></script>
<script src="{$base_url}dist/modules/nicescroll/jquery.nicescroll.min.js"></script>
<script src="{$base_url}dist/modules/moment.min.js"></script>
<script src="{$base_url}dist/sweetalert2/sweetalert2.min.js"></script>
<script src="{$base_url}dist/modules/time.js"></script>
<script src="{$base_url}dist/js/stisla.js"></script>

<script src="{$base_url}dist/modules/chart.min.js"></script>
<script src="{$base_url}dist/modules/datatables/datatables.min.js"></script>
<script src="{$base_url}dist/modules/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js"></script>
<script src="{$base_url}dist/modules/datatables/Select-1.2.4/js/dataTables.select.min.js"></script>
<script src="{$base_url}dist/modules/select2.full.min.js"></script>
<script src="{$base_url}dist/modules/jquery-ui/jquery-ui.min.js"></script>
<script src="{$base_url}dist/bootstrap/assets/jqueryform/jquery.form.js"></script>
<script src="{$base_url}dist/modules/summernote/summernote-bs4.min.js"></script>

<script src="{$base_url}dist/js/clipboard.min.js"></script>
<script src="{$base_url}dist/js/scripts.js"></script>
<script src="{$base_url}dist/js/custom-select.js"></script>
{include file='js/page/custom_js.tpl'}
{include file='js/page/notification_js.tpl'}
{include file='js/page/viewuser_js.tpl'}
{include file='js/page/search_js.tpl'}
</body>
</html>