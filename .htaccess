Options +FollowSymlinks -Indexes
RewriteEngine On

# LOCALHOST-SPECIFIC: Base URL
RewriteBase /RajaGenWeb/

# Allow Authorization and Host headers
RewriteRule .* - [E=Authorization:%{HTTP:Authorization},E=Host:%{HTTP:Host}]

############
# SECURITY #
############

# Block access to sensitive folders
RewriteRule ^(includes|content|templates|templates_c|uploads)/ - [F,L]

# Block access to sensitive files
<FilesMatch "\.(htaccess|htpasswd|ini|phps|fla|psd|log|sh|sql|json)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

<FilesMatch "^(config|db_config|install|setup|wp-config|configuration|bb-config|php\.ini|php5\.ini|db)\.php$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Prevent directory listing
IndexIgnore *

# Prevent server info
ServerSignature Off

#######################
# CLEAN URL REWRITING #
#######################

# Activation URL
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^activate/([0-9]+)/([^/.]+)/?$ index.php?p=activate&code=$1&email=$2 [L,QSA]

# Support ticket URL
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^supportticket/(.*)/([^/.]+)/?$ index.php?p=supportticket&id=$1&user=$2 [L,QSA]

# General pages: /dashboard => index.php?p=dashboard
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^([^/.]+)/?$ index.php?p=$1 [L,QSA]

#################
# XSS Protection
#################
<IfModule mod_headers.c>
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set X-Content-Type-Options nosniff
</IfModule>

##############################
# Disable ModSecurity (optional)
##############################
<IfModule mod_security.c>
    SecFilterEngine Off
    SecFilterScanPOST Off
</IfModule>

####################
# Protect .htaccess
####################
<Files ~ "^.*\.([Hh][Tt][Aa])">
    Order allow,deny
    Deny from all
</Files>
