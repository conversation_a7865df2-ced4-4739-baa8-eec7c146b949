<?php
// Test serverside routing without authentication
echo "<h2>Serverside Routing Test</h2>";

echo "<h3>Testing URL Routing:</h3>";

// Test if the routing works by checking what gets loaded
$test_urls = [
    'normal-serverside',
    'inactive-serverside', 
    'trial-serverside'
];

foreach($test_urls as $url) {
    echo "<h4>Testing: $url</h4>";
    
    // Simulate the index.php routing logic
    $p = $url;
    $ex = "php";
    $folder_content = "content";
    
    echo "Looking for: $folder_content/$p.$ex<br>";
    echo "File exists: " . (file_exists("$folder_content/$p.$ex") ? "YES" : "NO") . "<br>";
    
    echo "Looking for: $folder_content/serverside/$p.$ex<br>";
    echo "File exists: " . (file_exists("$folder_content/serverside/$p.$ex") ? "YES" : "NO") . "<br>";
    
    if(file_exists("$folder_content/serverside/$p.$ex")) {
        echo "✅ File found at: " . realpath("$folder_content/serverside/$p.$ex") . "<br>";
    }
    
    echo "<hr>";
}

echo "<h3>Test Direct Access:</h3>";
echo '<a href="http://192.168.0.106/RajaGenWeb/normal-serverside" target="_blank">Test normal-serverside</a><br>';
echo '<a href="http://192.168.0.106/RajaGenWeb/inactive-serverside" target="_blank">Test inactive-serverside</a><br>';
echo '<a href="http://192.168.0.106/RajaGenWeb/trial-serverside" target="_blank">Test trial-serverside</a><br>';

echo "<h3>Base URL Test:</h3>";
include './includes/functions.php';
echo "Base URL: " . $db->base_url() . "<br>";
echo "Normal serverside URL: " . $db->base_url() . "normal-serverside<br>";
?>
