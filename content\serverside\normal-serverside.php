<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if this is an AJAX request
$isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';

// For AJAX requests, return JSON error instead of redirecting
if(!is_logged_in($user)) {
    if($isAjax) {
        header('Content-Type: application/json');
        echo json_encode([
            "error" => true,
            "message" => "Session expired. Please log in again.",
            "code" => 401
        ]);
        exit;
    } else {
        header("Location: " . $db->base_url() . "login");
        exit;
    }
}

if($user_id_2 == 1 || $user_level_2 == 'superadmin' || $user_level_2 == 'reseller'){

}else{
    if($isAjax) {
        header('Content-Type: application/json');
        echo json_encode([
            "error" => true,
            "message" => "Access denied. Insufficient permissions.",
            "code" => 403
        ]);
        exit;
    } else {
        header("Location: " . $db->base_url() . "dashboard");
        exit;
    }
}

$requestData= $_REQUEST;
if(empty($requestData)){
	$db->RedirectToURL($db->base_url());
	exit;
}

$columns = array( 
    0	=> 'user_id',
	1	=> 'user_name',
	2	=> null
);

$sql = "SELECT * FROM users";
$query = $db->sql_query($sql) or die();
$totalData = $db->sql_numrows($query);
$totalFiltered = $totalData;
if($user_id_2 == 1 || $user_level_2 == 'superadmin'){
    $sql = "SELECT * FROM users WHERE 1=1 AND user_id!='$user_id_2' AND user_level='normal'";
}else{
	$sql = "SELECT * FROM users WHERE 1=1 AND user_id!='$user_id_2' AND upline='$user_id_2' AND user_level='normal'";
}

if( !empty($requestData['search']['value']) ) { 
	$sql.=" AND ( user_id LIKE '%".$requestData['search']['value']."%' "; 
	$sql.=" OR user_name LIKE '%".$requestData['search']['value']."%' ) ";
}

$query = $db->sql_query($sql) or die();
$totalFiltered = $db->sql_numrows($query);

// Fix the ORDER BY clause to handle null columns and missing parameters
$orderColumn = 'user_id'; // Default column
$orderDir = 'asc'; // Default direction

if(isset($requestData['order']) && isset($requestData['order'][0]) && isset($requestData['order'][0]['column'])) {
    $columnIndex = $requestData['order'][0]['column'];
    if(isset($columns[$columnIndex]) && $columns[$columnIndex] != null) {
        $orderColumn = $columns[$columnIndex];
    }
}

if(isset($requestData['order']) && isset($requestData['order'][0]) && isset($requestData['order'][0]['dir'])) {
    $orderDir = $requestData['order'][0]['dir'];
}

$start = isset($requestData['start']) ? intval($requestData['start']) : 0;
$length = isset($requestData['length']) ? intval($requestData['length']) : 10;

$sql.=" ORDER BY ". $orderColumn."  ".$orderDir."  LIMIT ".$start." ,".$length." ";

$query = $db->sql_query($sql) or die();


$data = array();
while( $row = $db->sql_fetchrow($query) ) {
	$nestedData=array();
	$userid = $row['user_id'];
	$username = $row['user_name'];
	$password = $row['user_pass'];
	$user_pass = $db->decrypt_key($password);
	$userpass = $db->encryptor('decrypt', $user_pass);
	$userduration = $row['duration'];
	$stat = $row['device_connected'];
	$is_online = $row['is_connected'];
	$active_date = $row['active_date'];
	$is_freeze = $row['is_freeze'];
	
	$bandwidth_qry = $db->sql_query("SELECT sum(bytes_sent) as bsent, sum(bytes_received) as breceived FROM bandwidth_logs WHERE username='$username'");
	$bandwidth_row = $db->sql_fetchrow($bandwidth_qry);
	
	$bytes_sent = $bandwidth_row['bsent'];
	$bytes_received = $bandwidth_row['breceived'];
	
	if($bytes_sent == 0 && $bytes_received == 0){
	    $bandwidth_used = '0B';
	}else{
    	$bandwidth = $bytes_sent + $bytes_received;
    	$bandwidth_used = convertToReadableSize($bandwidth);
	}
	
	$dur = $db->calc_time($userduration);	
	$pdays = $dur['days'] . " days";
	$phours = $dur['hours'] . " hours";
	$pminutes = $dur['minutes'] . " minutes";
	$pseconds = $dur['seconds'] . " seconds";
	
	$elapse = get_time_elapsed("$active_date");
	
	if($is_freeze == 0){
	    $is_blocked = '';
	    $badge_blocked = 'primary';
	}else{
	    $is_blocked = '<img src="dist/img/block.png" class="avatar-icon" alt="...">';
	    $badge_blocked = 'danger';
	}
	
	if($userduration == 0){
	    $duration = '<span class="badge badge-danger"><span class="fas fa-clock"></span> Expired</span>';
	}else{
		$duration = strtotime($pdays . $phours . $pminutes . $pseconds);
		$duration = '<span class="badge badge-primary"><span class="far fa-calendar-alt"></span> '.date('Y-m-d H:m:s', $duration).'</span>';
	}	
	
	if($stat == 0){
	    $expired = '<span class="badge badge-primary"><span class="far fa-calendar-alt"></span> none</span>';
	}else{
	    $expired = $duration;
	}
	
	if($is_online == 0){
	    $status = '<span class="badge badge-danger"><i class="fa fa-times-circle"></i> No</span>';
	}else{
	    $status = '<span class="badge badge-success"><i class="fa fa-check-circle"></i> Yes</span>';
	}
	
	if($active_date == '0000-00-00 00:00:00'){
	    $session = '<span class="badge badge-info"><i class="far fa-clock"></i> none</span>';
	}else{
	    $session = '<span class="badge badge-info"><i class="far fa-clock"></i> '.$elapse.'</span>';
	}
	
	$nestedData[] = '<span style="white-space:nowrap">
	                <figure class="avatar mr-2 avatar-sm border-primary">
                      <img src="profile/avatar-1.png" alt="'.$username.'">
                      '.$is_blocked.'
                    </figure> <span class="badge badge-'.$badge_blocked.'"><a class="username-class" onclick="user_option('.$userid.')">'.$username.'</a></span></span>';
	$nestedData[] = $status;
	$nestedData[] = $session;
	$nestedData[] = '<span class="badge badge-info"><i class="fas fa-tachometer-alt"></i> '.$bandwidth_used.'</span>';
	$nestedData[] = $expired;
	$nestedData[] = '<div class="btn-group btn-group-md" role="group">
                    	<button type="button" class="btn btn-primary mr-1" onclick="view_info('.$userid.')"><i class="far fa-eye"></i></button>
                    	<button type="button" class="btn btn-primary mr-1" onclick="user_option('.$userid.')"><i class="fas fa-edit"></i></button>
                    	<button type="button" class="btn btn-success btn-copy" data-clipboard-text="User Details 

Username : '.$username.' 
Password : '.$userpass.'" data-id="'.$userid.'"><i class="far fa-copy"></i></button>
                    </div>';

	$data[] = $nestedData;	
}

$json_data = array(
			"draw"            => isset($requestData['draw']) ? intval($requestData['draw']) : 1,
			"recordsTotal"    => intval( $totalData ),
			"recordsFiltered" => intval( $totalFiltered ),
			"data"            => ($data )
			);

header('Content-Type: application/json');
echo json_encode($json_data);
?>